-- Test Runner
-- Runs all unit tests using TestEZ framework

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for TestEZ (if using Wally)
local TestEZ = require(ReplicatedStorage:WaitForChild("Packages"):WaitForChild("TestEZ"))

-- Run tests
print("🧪 Starting SupaMan Tests...")

local results = TestEZ.TestBootstrap:run({
    ReplicatedStorage.Tests
})

if results.failureCount == 0 then
    print("✅ All tests passed!")
else
    print("❌ " .. results.failureCount .. " tests failed")
end
