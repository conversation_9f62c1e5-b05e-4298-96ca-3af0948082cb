-- SuperStrength module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- TODO: Implement super strength logic
    -- This should deal Config.POWERS.SuperStrength.dmg damage to nearby enemies
    print("SuperStrength activated by", player.Name)

    -- visual (fire strength effect on all clients)
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("SuperStrength", root.Position, mouseHit)
end
