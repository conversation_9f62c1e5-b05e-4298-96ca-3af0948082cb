local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    -- TODO: Implement Super Strength power
    -- This power should deal high damage to enemies
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Add super strength implementation here
    print("Super Strength activated by", player.Name)
    
    -- Fire to all clients for visual effects
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("SuperStrength", root.Position, mouseHit)
end
