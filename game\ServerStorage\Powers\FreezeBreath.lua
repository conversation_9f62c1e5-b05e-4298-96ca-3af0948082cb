-- FreezeBreath module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- TODO: Implement freeze breath logic
    -- This should freeze enemies for Config.POWERS.FreezeBreath.duration seconds
    print("FreezeBreath activated by", player.Name)

    -- visual (fire freeze effect on all clients)
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("FreezeBreath", root.Position, mouseHit)
end
