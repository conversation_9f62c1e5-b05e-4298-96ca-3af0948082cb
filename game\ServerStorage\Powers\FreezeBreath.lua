local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    -- TODO: Implement Freeze Breath power
    -- This power should freeze enemies for the duration specified in Config
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Add freeze breath implementation here
    print("Freeze Breath activated by", player.Name)
    
    -- Fire to all clients for visual effects
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("FreezeBreath", root.Position, mouseHit)
end
