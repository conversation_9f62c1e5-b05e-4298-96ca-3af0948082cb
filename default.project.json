{"name": "<PERSON><PERSON><PERSON><PERSON>", "tree": {"$className": "DataModel", "ReplicatedStorage": {"SupermanShared": {"$className": "Folder", "Config": {"$path": "game/Replicated/SupermanShared/Config.lua"}, "FlightRemote": {"$className": "RemoteEvent"}, "PowerRemote": {"$className": "RemoteEvent"}}}, "ServerScriptService": {"$path": "game/Server"}, "ServerStorage": {"Powers": {"$path": "game/ServerStorage/Powers"}}, "StarterPlayer": {"StarterPlayerScripts": {"$path": "game/Client"}}, "StarterGui": {"ShopGui": {"$path": "game/StarterGui/ShopGui"}}}}