-- Shared utilities and constants
-- This module is accessible from both client and server

local Shared = {}

-- Game constants
Shared.Constants = {
    GAME_NAME = "SupaMan",
    VERSION = "1.0.0",
    MAX_PLAYERS = 50,
}

-- Utility functions
function Shared.deepCopy(original)
    local copy = {}
    for key, value in pairs(original) do
        if type(value) == "table" then
            copy[key] = Shared.deepCopy(value)
        else
            copy[key] = value
        end
    end
    return copy
end

function Shared.formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%02d:%02d", minutes, remainingSeconds)
end

return Shared
