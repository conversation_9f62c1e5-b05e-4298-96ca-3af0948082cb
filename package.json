{"name": "supa<PERSON>", "version": "1.0.0", "description": "Superman-themed Roblox game with flight, superpowers, and modern development tools", "scripts": {"build": "rojo build -o SupaMan.rbxl", "serve": "rojo serve", "lint": "selene game/", "test": "rojo build test.project.json -o test.rbxl && echo 'Run test.rbxl in Roblox Studio to execute tests'"}, "devDependencies": {}, "keywords": ["roblo<PERSON>", "superman", "flight", "superpowers", "game"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}