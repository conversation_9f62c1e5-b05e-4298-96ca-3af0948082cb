-- Local Script Example
-- This script runs on each client (player's computer)

print("Hello from the client!")

-- Example: Handle user input
local UserInputService = game:GetService("UserInputService")
local Players = game:GetService("Players")

local player = Players.LocalPlayer

UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.Space then
        print("Space key pressed by " .. player.Name)
    end
end)
