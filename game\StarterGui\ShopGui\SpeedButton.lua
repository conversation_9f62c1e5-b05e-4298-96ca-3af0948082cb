-- SpeedButton - TextButton for purchasing speed boosts
-- This file creates the TextButton instance

local SpeedButton = Instance.new("TextButton")
SpeedButton.Name = "SpeedButton"
SpeedButton.Size = UDim2.new(0, 200, 0, 50)
SpeedButton.Position = UDim2.new(0.5, -100, 0.5, -25)
SpeedButton.BackgroundColor3 = Color3.fromRGB(0, 162, 255)
SpeedButton.BorderSizePixel = 0
SpeedButton.Font = Enum.Font.SourceSansBold
SpeedButton.Text = "Buy Speed Boost"
SpeedButton.TextColor3 = Color3.fromRGB(255, 255, 255)
SpeedButton.TextSize = 18

-- Add corner rounding
local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 8)
corner.Parent = SpeedButton

return SpeedButton
