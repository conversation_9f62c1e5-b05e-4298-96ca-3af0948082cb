-- Main client script
-- Handles client-side game logic and UI

print("🎮 SupaMan Client Starting...")

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local StarterGui = game:GetService("StarterGui")

-- Wait for character and shared modules
local player = Players.LocalPlayer
local character = player.CharacterAdded:Wait()
local humanoid = character:WaitFor<PERSON>hild("Humanoid")

-- Load shared modules
local Shared = require(ReplicatedStorage:WaitForChild("Shared"))

print("📦 Loaded Shared modules")
print("🎮 Welcome to", Shared.Constants.GAME_NAME, "v" .. Shared.Constants.VERSION)

-- Input handling
local function onInputBegan(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.Space then
        print("🚀 Space pressed - Jump boost!")
        humanoid.JumpPower = 100
        wait(0.1)
        humanoid.JumpPower = 50
    elseif input.KeyCode == Enum.KeyCode.E then
        print("⚡ E pressed - Action!")
        -- Add your action logic here
    end
end

-- Connect input events
UserInputService.InputBegan:Connect(onInputBegan)

-- UI Setup
StarterGui:SetCore("TopbarEnabled", true)
StarterGui:SetCore("ChatActive", true)

print("✅ SupaMan Client Ready!")
