# SupaMan 🦸‍♂️

A professional Roblox game built with modern development tools and best practices.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run serve
   # or
   rojo serve
   ```

3. **Connect Roblox Studio**
   - Install the Rojo plugin in Roblox Studio
   - Click "Connect" in the plugin
   - Your code will sync automatically!

## 📁 Project Structure

```
SupaMan/
├── game/                    # Game source code
│   ├── Client/             # Client-side scripts
│   ├── Server/             # Server-side scripts
│   ├── Shared/             # Shared utilities
│   └── Replicated/         # ReplicatedStorage content
├── test/                   # Unit tests
├── default.project.json    # Rojo configuration
├── wally.toml             # Package dependencies
├── selene.toml            # Linter configuration
└── package.json           # Build scripts
```

## 🛠️ Development Tools

- **Rojo** - File syncing between VS Code and Roblox Studio
- **Wally** - Package manager for Roblox libraries
- **Selene** - Luau linter for code quality
- **TestEZ** - Unit testing framework
- **Roblox LSP** - Autocomplete and type checking

## 📝 Available Scripts

- `npm run serve` - Start Rojo development server
- `npm run build` - Build game file for production
- `npm run lint` - Run code linter
- `npm run test` - Run unit tests

## 🧪 Testing

Run tests with:
```bash
npm run test
```

This builds a test place file that you can open in Roblox Studio to run the tests.

## 📦 Adding Dependencies

Add Roblox packages using Wally:
```bash
wally install
```

## 🎮 Game Features

- Professional code structure
- Client-server communication
- Player management system
- Leaderstats integration
- Input handling
- Comprehensive testing

---

Built with ❤️ using modern Roblox development tools
