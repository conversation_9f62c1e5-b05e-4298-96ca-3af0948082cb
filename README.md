# SupaMan 🦸‍♂️

A professional Superman-themed Roblox game with flight, superpowers, and modern development tools.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run serve
   # or
   rojo serve
   ```

3. **Connect Roblox Studio**
   - Install the Rojo plugin in Roblox Studio
   - Click "Connect" in the plugin
   - Your code will sync automatically!

## 📁 Project Structure

```
SupaMan/
├── game/                           # Game source code
│   ├── Client/                     # Client-side scripts
│   │   └── ClientFlight.client.lua # Flight system & input handling
│   ├── Server/                     # Server-side scripts
│   │   └── SupermanMain.server.lua # Main server logic
│   ├── ServerStorage/              # Server-only content
│   │   └── Powers/                 # Power modules
│   │       ├── HeatVision.lua      # Heat vision implementation
│   │       ├── FreezeBreath.lua    # Freeze breath power
│   │       └── SuperStrength.lua   # Super strength power
│   ├── Replicated/                 # ReplicatedStorage content
│   │   └── SupermanShared/         # Shared modules & remotes
│   │       ├── Config.lua          # Game configuration
│   │       ├── FlightRemote.lua    # Flight communication
│   │       └── PowerRemote.lua     # Power communication
│   └── StarterGui/                 # UI elements
│       └── ShopGui/                # Speed boost shop
├── test/                           # Unit tests
├── default.project.json            # Rojo configuration
├── wally.toml                      # Package dependencies
├── selene.toml                     # Linter configuration
└── package.json                    # Build scripts
```

## 🛠️ Development Tools

- **Rojo** - File syncing between VS Code and Roblox Studio
- **Wally** - Package manager for Roblox libraries
- **Selene** - Luau linter for code quality
- **TestEZ** - Unit testing framework
- **Roblox LSP** - Autocomplete and type checking

## 📝 Available Scripts

- `npm run serve` - Start Rojo development server
- `npm run build` - Build game file for production
- `npm run lint` - Run code linter
- `npm run test` - Run unit tests

## 🧪 Testing

Run tests with:
```bash
npm run test
```

This builds a test place file that you can open in Roblox Studio to run the tests.

## 📦 Adding Dependencies

Add Roblox packages using Wally:
```bash
wally install
```

## 🦸‍♂️ Game Features

### ✈️ **Flight System**
- **Auto-Flight** - Activates when falling or jumping
- **W/S** - Fly forward/backward in camera direction
- **A/D** - Strafe left/right relative to camera
- **Space** - Fly up (world space)
- **Left Shift** - Fly down (world space)
- **Mouse Look** - Control flight direction by looking around
- **Character Orientation** - Character faces movement direction

### ⚡ **Superpowers**
- **Heat Vision** - Hold mouse/tap to fire continuous laser beams from eyes (25 damage)
  - Beams originate from character's eyes and aim where mouse points
  - Continuous damage while held
  - Rate limited to prevent spam (0.25s cooldown)
  - Visual red laser beams with neon effect
- **Freeze Breath** - Freeze enemies for 3 seconds (TODO: Implementation)
- **Super Strength** - High damage melee attacks (TODO: Implementation)

### 🏪 **Shop System**
- **Speed Boosts** - Purchase permanent speed increases
- **Developer Products** - Integrated marketplace system
- **Data Persistence** - Saves purchases across sessions

### 🛡️ **Anti-Exploit**
- **Speed Detection** - Automatically kicks speed hackers
- **Network Ownership** - Proper character ownership management
- **Secure Communication** - Server-validated power usage

### 🎮 **Technical Features**
- **Modular Powers** - Easy to add new superpowers
- **Cross-Platform** - Works on PC, mobile, and console
- **Professional Architecture** - Clean, maintainable code structure
- **Data Stores** - Persistent player data
- **Visual Effects** - Neon laser beams and particle effects

---

Built with ❤️ using modern Roblox development tools
