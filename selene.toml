std = "roblox"

[config]
unused_variable = "warn"
undefined_variable = "error"
incorrect_standard_library_use = "error"
duplicate_keys = "error"
empty_if = "warn"
if_same_then_else = "warn"
mismatched_arg_count = "error"
too_many_arguments = "error"
unused_function = "warn"
global_usage = "warn"

[rules]
suspicious_reverse_loop = "warn"
roblox_incorrect_roact_usage = "warn"
roblox_incorrect_color3_new_bounds = "error"
