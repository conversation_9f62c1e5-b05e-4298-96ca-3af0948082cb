-- Main server script
-- Handles server-side game logic

print("🚀 SupaMan Server Starting...")

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Load shared modules
local Shared = require(ReplicatedStorage.Shared)

print("📦 Loaded Shared modules")
print("🎮 Game:", Shared.Constants.GAME_NAME, "v" .. Shared.Constants.VERSION)

-- Player management
local function onPlayerAdded(player)
    print("👋 Player joined:", player.Name)
    
    -- Create leaderstats
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player
    
    local score = Instance.new("IntValue")
    score.Name = "Score"
    score.Value = 0
    score.Parent = leaderstats
    
    local level = Instance.new("IntValue")
    level.Name = "Level"
    level.Value = 1
    level.Parent = leaderstats
end

local function onPlayerRemoving(player)
    print("👋 Player left:", player.Name)
    -- Save player data here
end

-- Connect events
Players.PlayerAdded:Connect(onPlayerAdded)
Players.PlayerRemoving:Connect(onPlayerRemoving)

-- Handle players already in game
for _, player in pairs(Players:GetPlayers()) do
    onPlayerAdded(player)
end

print("✅ SupaMan Server Ready!")
