-- Remote Events for client-server communication
-- This creates the remote events that both client and server can use

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create a folder for remote events
local remoteEventsFolder = Instance.new("Folder")
remoteEventsFolder.Name = "RemoteEvents"
remoteEventsFolder.Parent = ReplicatedStorage

-- Example remote events
local playerActionEvent = Instance.new("RemoteEvent")
playerActionEvent.Name = "PlayerAction"
playerActionEvent.Parent = remoteEventsFolder

local updateScoreEvent = Instance.new("RemoteEvent")
updateScoreEvent.Name = "UpdateScore"
updateScoreEvent.Parent = remoteEventsFolder

-- Remote functions
local remoteFunction = Instance.new("RemoteFunction")
remoteFunction.Name = "GetPlayerData"
remoteFunction.Parent = remoteEventsFolder

print("📡 Remote Events created successfully")
