local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MarketplaceService = game:GetService("MarketplaceService")
local DataStoreService = game:GetService("DataStoreService")

local shared = ReplicatedStorage:WaitForChild("SupermanShared")
local Config = require(shared.Config)
local PowerRemote = shared.PowerRemote
local speedBoostStore = DataStoreService:GetDataStore("SpeedBoosts")

local playerSpeeds = {}

local function setNetworkOwner(model)
    for _, v in pairs(model:GetDescendants()) do if v:IsA("BasePart") then v:SetNetworkOwner(nil) end end
end

local function loadData(player)
    local ok, owns = pcall(function() return speedBoostStore:GetAsync(player.UserId) end)
    return ok and owns or false
end
local function saveData(player, owns)
    pcall(function() speedBoostStore:SetAsync(player.UserId, owns) end)
end

local function onChar(char, plr)
    setNetworkOwner(char)
    local speedMult = loadData(plr) and Config.SPEED_MULTIPLIER or 1
    playerSpeeds[plr] = Config.MAX_SPEED * speedMult
end
Players.PlayerAdded:Connect(function(p)
    p.CharacterAdded:Connect(function(c) onChar(c, p) end)
    if p.Character then onChar(p.Character, p) end
end)
Players.PlayerRemoving:Connect(function(p)
    saveData(p, playerSpeeds[p] and playerSpeeds[p] > Config.MAX_SPEED)
    playerSpeeds[p] = nil
end)

-- anti-exploit
task.spawn(function()
    while true do
        task.wait(1)
        for _, p in ipairs(Players:GetPlayers()) do
            local char = p.Character
            if not char then continue end
            local root = char:FindFirstChild("HumanoidRootPart")
            if not root then continue end
            if root.Velocity.Magnitude > (playerSpeeds[p] or Config.MAX_SPEED) + 5 then
                p:Kick("Speed exploit detected.")
            end
        end
    end
end)

-- dev product
MarketplaceService.ProcessReceipt = function(receiptInfo)
    local p = Players:GetPlayerByUserId(receiptInfo.PlayerId)
    if not p then return Enum.ProductPurchaseDecision.NotProcessedYet end
    if receiptInfo.ProductId == Config.DEV_PRODUCT_SPEED then
        playerSpeeds[p] = Config.MAX_SPEED * Config.SPEED_MULTIPLIER
        saveData(p, true)
        return Enum.ProductPurchaseDecision.PurchaseGranted
    end
    return Enum.ProductPurchaseDecision.NotProcessedYet
end

-- power glue
PowerRemote.OnServerEvent:Connect(function(player, powerName, ...)
    local mod = script.Parent:FindFirstChild("Powers") and
                script.Parent.Powers:FindFirstChild(powerName)
    if mod then require(mod)(player, ...) end
end)
