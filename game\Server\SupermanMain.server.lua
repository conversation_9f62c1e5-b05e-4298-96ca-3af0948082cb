-- SupermanMain
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MarketplaceService = game:GetService("MarketplaceService")
local DataStoreService = game:GetService("DataStoreService")

local shared = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared")
local Config = require(shared.Config)
local FlightRemote = shared.FlightRemote
local PowerRemote = shared.PowerRemote
local speedBoostStore = DataStoreService:GetDataStore("SpeedBoosts")

-- anti-exploit table
local playerSpeeds = {}

------------------------------------------------------------------
-- UTILITY
------------------------------------------------------------------
local function setNetworkOwner(model)
    for _, v in pairs(model:GetDescendants()) do
        if v:IsA("BasePart") then v:SetNetworkOwner(nil) end
    end
end

------------------------------------------------------------------
-- DATASTORE
------------------------------------------------------------------
local function loadData(player)
    local success, owns = pcall(function()
        return speedBoostStore:GetAsync(player.UserId)
    end)
    return success and owns or false
end

local function saveData(player, owns)
    pcall(function()
        speedBoostStore:SetAsync(player.UserId, owns)
    end)
end

------------------------------------------------------------------
-- CHARACTER SETUP
------------------------------------------------------------------
local function onCharacterAdded(char, player)
    setNetworkOwner(char)
    local root = char:WaitForChild("HumanoidRootPart")
    local speedMult = loadData(player) and Config.SPEED_MULTIPLIER or 1
    playerSpeeds[player] = Config.MAX_SPEED * speedMult
end

local function onPlayerAdded(player)
    player.CharacterAdded:Connect(function(char) onCharacterAdded(char, player) end)
    if player.Character then onCharacterAdded(player.Character, player) end
end

local function onPlayerRemoving(player)
    saveData(player, playerSpeeds[player] and playerSpeeds[player] > Config.MAX_SPEED)
    playerSpeeds[player] = nil
end

Players.PlayerAdded:Connect(onPlayerAdded)
Players.PlayerRemoving:Connect(onPlayerRemoving)

------------------------------------------------------------------
-- ANTI-EXPLOIT
------------------------------------------------------------------
spawn(function()
    while true do
        task.wait(1)
        for _, player in ipairs(Players:GetPlayers()) do
            local char = player.Character
            if not char then continue end
            local root = char:FindFirstChild("HumanoidRootPart")
            if not root then continue end

            local vel = root.Velocity.Magnitude
            if vel > (playerSpeeds[player] or Config.MAX_SPEED) + 5 then
                player:Kick("Speed exploit detected.")
            end
        end
    end
end)

------------------------------------------------------------------
-- DEV PRODUCT
------------------------------------------------------------------
MarketplaceService.ProcessReceipt = function(receiptInfo)
    local player = Players:GetPlayerByUserId(receiptInfo.PlayerId)
    if not player then return Enum.ProductPurchaseDecision.NotProcessedYet end

    if receiptInfo.ProductId == Config.DEV_PRODUCT_SPEED then
        playerSpeeds[player] = Config.MAX_SPEED * Config.SPEED_MULTIPLIER
        saveData(player, true)
        return Enum.ProductPurchaseDecision.PurchaseGranted
    end
    return Enum.ProductPurchaseDecision.NotProcessedYet
end

------------------------------------------------------------------
-- POWER REMOTE BINDING
------------------------------------------------------------------
-- glue powers to remote
PowerRemote.OnServerEvent:Connect(function(player, powerName, ...)
    local module = game.ServerStorage:FindFirstChild("Powers") and game.ServerStorage.Powers:FindFirstChild(powerName)
    if module then require(module)(player, ...) end
end)
