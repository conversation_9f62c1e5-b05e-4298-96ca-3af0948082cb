-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
local remote = ReplicatedStorage:WaitForChild("SupermanShared"):WaitFor<PERSON>hild("FlightRemote")
local powerRemote = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared"):Wait<PERSON><PERSON><PERSON>hild("PowerRemote")

------------------------------------------------------------------
-- BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- FLIGHT LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
            print("✈️ Flight activated! Use WASD to fly, mouse to steer")
        end
    else
        local camera = workspace.CurrentCamera
        local cameraCFrame = camera.CFrame

        -- Calculate movement direction based on camera orientation
        local moveVector = Vector3.new(0, 0, 0)

        -- Forward/Backward (W/S) - move in camera's forward direction
        if w then
            moveVector = moveVector + cameraCFrame.LookVector
        end
        if s then
            moveVector = moveVector - cameraCFrame.LookVector
        end

        -- Left/Right (A/D) - strafe relative to camera
        if a then
            moveVector = moveVector - cameraCFrame.RightVector
        end
        if d then
            moveVector = moveVector + cameraCFrame.RightVector
        end

        -- Up/Down (Space/Shift) - world space up/down
        if q then -- Space
            moveVector = moveVector + Vector3.new(0, 1, 0)
        end
        if e then -- Left Shift
            moveVector = moveVector + Vector3.new(0, -1, 0)
        end

        -- Normalize and apply speed
        local flightSpeed = 80
        if moveVector.Magnitude > 0 then
            moveVector = moveVector.Unit * flightSpeed
        end

        -- Apply movement with slight smoothing for better feel
        vel.Velocity = moveVector

        -- Make character face the direction they're moving
        if moveVector.Magnitude > 0 then
            -- Face movement direction (horizontal only for natural look)
            local lookDirection = Vector3.new(moveVector.X, 0, moveVector.Z)
            if lookDirection.Magnitude > 0 then
                gyro.CFrame = CFrame.lookAt(root.Position, root.Position + lookDirection)
            end
        else
            -- If not moving, face camera direction (horizontal only)
            local cameraLook = Vector3.new(cameraCFrame.LookVector.X, 0, cameraCFrame.LookVector.Z)
            if cameraLook.Magnitude > 0 then
                gyro.CFrame = CFrame.lookAt(root.Position, root.Position + cameraLook)
            end
        end
    end
end)

------------------------------------------------------------------
-- HEAT VISION SYSTEM
------------------------------------------------------------------
local heatVisionActive = false
local heatVisionBeams = {}

-- Create heat vision beams from eyes to target
local function createHeatVisionBeams(targetPos)
    -- Remove old beams
    for _, beam in pairs(heatVisionBeams) do
        if beam and beam.Parent then
            beam:Destroy()
        end
    end
    heatVisionBeams = {}

    if not char or not char:FindFirstChild("Head") then return end

    -- Get eye positions
    local head = char.Head
    local headCFrame = head.CFrame
    local eyeOffset = 0.15
    local rightEye = headCFrame * CFrame.new(eyeOffset, 0.1, -0.5)
    local leftEye = headCFrame * CFrame.new(-eyeOffset, 0.1, -0.5)

    -- Create beams from each eye to target
    for i, eyePos in pairs({rightEye.Position, leftEye.Position}) do
        local distance = (targetPos - eyePos).Magnitude
        local beam = Instance.new("Part")
        beam.Name = "HeatVisionBeam"
        beam.Anchored = true
        beam.CanCollide = false
        beam.Size = Vector3.new(0.1, 0.1, distance)
        beam.CFrame = CFrame.new(eyePos, targetPos) * CFrame.new(0, 0, -distance / 2)
        beam.BrickColor = BrickColor.new("Bright red")
        beam.Material = Enum.Material.Neon
        beam.Transparency = 0.2
        beam.Parent = workspace

        table.insert(heatVisionBeams, beam)
    end
end

-- Update heat vision continuously
RunService.Heartbeat:Connect(function()
    if heatVisionActive then
        -- Get mouse position in world
        local mouse = player:GetMouse()
        local camera = workspace.CurrentCamera

        local rayOrigin = camera.CFrame.Position
        local rayDirection = (mouse.Hit.Position - rayOrigin).Unit * 500

        local raycastParams = RaycastParams.new()
        raycastParams.FilterDescendantsInstances = {char}
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist

        local raycastResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)
        local hitPos = raycastResult and raycastResult.Position or mouse.Hit.Position

        -- Create beams to hit position
        createHeatVisionBeams(hitPos)

        -- Fire server event for damage
        powerRemote:FireServer("HeatVision", hitPos)
    end
end)

------------------------------------------------------------------
-- HEAT VISION INPUT
------------------------------------------------------------------
-- Mouse/touch input for heat vision
UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end

    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = true
        print("🔥 Heat vision activated!")
    end
end)

UserInputService.InputEnded:Connect(function(input, gpe)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = false

        -- Clean up beams
        for _, beam in pairs(heatVisionBeams) do
            if beam and beam.Parent then
                beam:Destroy()
            end
        end
        heatVisionBeams = {}
        print("🔥 Heat vision deactivated!")
    end
end)
