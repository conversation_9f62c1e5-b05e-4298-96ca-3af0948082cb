-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
local remote = ReplicatedStorage:WaitForChild("SupermanShared"):WaitFor<PERSON>hild("FlightRemote")
local powerRemote = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared"):Wait<PERSON><PERSON><PERSON>hild("PowerRemote")

------------------------------------------------------------------
-- BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- FLIGHT LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
            print("✈️ Flight activated! Use WASD to fly, mouse to steer")
        end
    else
        local camera = workspace.CurrentCamera
        local cameraCFrame = camera.CFrame

        -- Calculate movement direction based on camera orientation
        local moveVector = Vector3.new(0, 0, 0)

        -- Forward/Backward (W/S) - move in camera's look direction (full 3D)
        if w then
            moveVector = moveVector + cameraCFrame.LookVector
        end
        if s then
            moveVector = moveVector - cameraCFrame.LookVector
        end

        -- Left/Right (A/D) - strafe relative to camera
        if a then
            moveVector = moveVector - cameraCFrame.RightVector
        end
        if d then
            moveVector = moveVector + cameraCFrame.RightVector
        end

        -- Up/Down (Space/Shift) - relative to camera's up vector for true 3D flight
        if q then -- Space
            moveVector = moveVector + cameraCFrame.UpVector
        end
        if e then -- Left Shift
            moveVector = moveVector - cameraCFrame.UpVector
        end

        -- Apply full 3D movement - no gravity, complete freedom
        local flightSpeed = 80
        if moveVector.Magnitude > 0 then
            vel.Velocity = moveVector.Unit * flightSpeed
        else
            vel.Velocity = Vector3.zero  -- Complete stop when no input
        end

        -- Character orientation for true 3D flight
        if moveVector.Magnitude > 0 then
            -- Face the direction of movement in full 3D space
            gyro.CFrame = CFrame.lookAt(root.Position, root.Position + moveVector)
        else
            -- When not moving, face camera direction in full 3D
            gyro.CFrame = CFrame.lookAt(root.Position, root.Position + cameraCFrame.LookVector)
        end
    else
        -- When not flying, restore normal physics
        vel.Velocity = Vector3.zero
        humanoid.PlatformStand = false
    end
end)

-- F key to toggle flight on/off
UserInputService.InputBegan:Connect(function(i,g)
    if g then return end
    if i.KeyCode == Enum.KeyCode.F then
        flying = not flying
        if flying then
            humanoid.PlatformStand = true
            print("✈️ Flight activated! Full 3D movement enabled")
        else
            humanoid.PlatformStand = false
            vel.Velocity = Vector3.zero
            print("🚶 Flight deactivated - walking mode")
        end
    end
end)


------------------------------------------------------------------
-- HEAT VISION SYSTEM
------------------------------------------------------------------
local heatVisionActive = false
local heatVisionBeams = {}

-- Create heat vision beams from eyes to target
local function createHeatVisionBeams(targetPos)
    -- Remove old beams
    for _, beam in pairs(heatVisionBeams) do
        if beam and beam.Parent then
            beam:Destroy()
        end
    end
    heatVisionBeams = {}

    if not char or not char:FindFirstChild("Head") then return end

    -- Get eye positions
    local head = char.Head
    local headCFrame = head.CFrame
    local eyeOffset = 0.15
    local rightEye = headCFrame * CFrame.new(eyeOffset, 0.1, -0.5)
    local leftEye = headCFrame * CFrame.new(-eyeOffset, 0.1, -0.5)

    -- Create beams from each eye to target
    for i, eyePos in pairs({rightEye.Position, leftEye.Position}) do
        local distance = (targetPos - eyePos).Magnitude
        local beam = Instance.new("Part")
        beam.Name = "HeatVisionBeam"
        beam.Anchored = true
        beam.CanCollide = false
        beam.Size = Vector3.new(0.1, 0.1, distance)
        beam.CFrame = CFrame.new(eyePos, targetPos) * CFrame.new(0, 0, -distance / 2)
        beam.BrickColor = BrickColor.new("Bright red")
        beam.Material = Enum.Material.Neon
        beam.Transparency = 0.2
        beam.Parent = workspace

        table.insert(heatVisionBeams, beam)
    end
end

-- Update heat vision continuously
RunService.Heartbeat:Connect(function()
    if heatVisionActive then
        -- Get mouse position in world
        local mouse = player:GetMouse()
        local camera = workspace.CurrentCamera

        local rayOrigin = camera.CFrame.Position
        local rayDirection = (mouse.Hit.Position - rayOrigin).Unit * 500

        local raycastParams = RaycastParams.new()
        raycastParams.FilterDescendantsInstances = {char}
        raycastParams.FilterType = Enum.RaycastFilterType.Blacklist

        local raycastResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)
        local hitPos = raycastResult and raycastResult.Position or mouse.Hit.Position

        -- Create beams to hit position
        createHeatVisionBeams(hitPos)

        -- Fire server event for damage
        powerRemote:FireServer("HeatVision", hitPos)
    end
end)

------------------------------------------------------------------
-- HEAT VISION INPUT
------------------------------------------------------------------
-- Mouse/touch input for heat vision
UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end

    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = true
        print("🔥 Heat vision activated!")
    end
end)

UserInputService.InputEnded:Connect(function(input, gpe)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = false

        -- Clean up beams
        for _, beam in pairs(heatVisionBeams) do
            if beam and beam.Parent then
                beam:Destroy()
            end
        end
        heatVisionBeams = {}
        print("🔥 Heat vision deactivated!")
    end
end)
