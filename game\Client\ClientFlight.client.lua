local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

local player = Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")

-- Body movers
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

-- Input states
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false
local function updateInputs(input, state)
    local k = input.KeyCode
    if k == Enum.KeyCode.W then w = state end
    if k == Enum.KeyCode.A then a = state end
    if k == Enum.KeyCode.S then s = state end
    if k == Enum.KeyCode.D then d = state end
    if k == Enum.KeyCode.Space then q = state end
    if k == Enum.KeyCode.LeftShift then e = state end
end
UserInputService.InputBegan:Connect(function(i) updateInputs(i, true) end)
UserInputService.InputEnded:Connect(function(i) updateInputs(i, false) end)

-- Flight loop
RunService.RenderStepped:Connect(function()
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
        end
    else
        local cam = workspace.CurrentCamera
        local move = Vector3.new(
            (d and 1 or 0) + (a and -1 or 0),
            (q and 1 or 0) + (e and -1 or 0),
            (s and 1 or 0) + (w and -1 or 0)
        )
        move = cam:VectorToWorldSpace(move)
        vel.Velocity = move * 80
        gyro.CFrame = cam
    end
end)

-- Beam FX
ReplicatedStorage:WaitForChild("SupermanShared").PowerRemote.OnClientEvent:Connect(function(name, start, finish)
    if name == "HeatVision" then
        local b = Instance.new("Part")
        b.Anchored, b.CanCollide = true, false
        b.Size = Vector3.new(0.2, 0.2, (finish - start).Magnitude)
        b.CFrame = CFrame.new(start, finish) * CFrame.new(0, 0, -b.Size.Z / 2)
        b.BrickColor = BrickColor.new("Bright red")
        b.Material = Enum.Material.Neon
        b.Parent = workspace
        game:GetService("Debris"):AddItem(b, 0.15)
    end
end)

-- Click / tap to fire
local function fire()
    local cam = workspace.CurrentCamera
    local params = RaycastParams.new()
    params.FilterDescendantsInstances = {char}
    params.FilterType = Enum.RaycastFilterType.Blacklist

    local origin, dir
    if UserInputService.TouchEnabled and not UserInputService.MouseEnabled then
        origin = cam.CFrame.Position
        dir = (cam.CFrame * CFrame.new(0, 0, -1)).Position - origin
    else
        local mouse = player:GetMouse()
        origin = cam.CFrame.Position
        dir = (mouse.Hit.Position - origin).Unit
    end

    local res = workspace:Raycast(origin, dir * 500, params)
    local hit = res and res.Position or origin + dir * 500

    ReplicatedStorage:WaitForChild("SupermanShared").PowerRemote:FireServer("HeatVision", hit)
end

UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        fire()
    end
end)
