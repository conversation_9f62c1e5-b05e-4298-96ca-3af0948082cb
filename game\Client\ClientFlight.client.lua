-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer

-- Wait for shared folder and remotes
local shared = ReplicatedStorage:WaitFor<PERSON>hild("SupermanShared")
local flightRemote = shared:WaitFor<PERSON>hild("FlightRemote")
local powerRemote = shared:WaitForChild("PowerRemote")

-- Character variables (will be set when character spawns)
local char, root, humanoid, gyro, vel

-- Input states
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

------------------------------------------------------------------
-- CHARACTER SETUP
------------------------------------------------------------------
local function setupC<PERSON>cter(character)
    char = character
    root = char:WaitForChild("HumanoidRootPart")
    humanoid = char:WaitForChild("Humanoid")

    -- Create body movers
    gyro = Instance.new("BodyGyro")
    gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
    gyro.P = 10000
    gyro.Parent = root

    vel = Instance.new("BodyVelocity")
    vel.MaxForce = Vector3.new(400000, 400000, 400000)
    vel.Parent = root

    -- Reset flight state
    flying = false

    print("🦸‍♂️ Superman powers loaded for", player.Name)
    print("✈️ Press F to toggle flight")
    print("🔥 Hold mouse button for heat vision")
end

-- Setup character when it spawns
if player.Character then
    setupCharacter(player.Character)
end
player.CharacterAdded:Connect(setupCharacter)

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end

    -- Toggle flight with F key
    if key == Enum.KeyCode.F and state then
        flying = not flying
        if char and humanoid then
            if flying then
                humanoid.PlatformStand = true
                print("Flight enabled! Use WASD + Space/Shift to fly")
            else
                humanoid.PlatformStand = false
                if vel then vel.Velocity = Vector3.new(0, 0, 0) end
                print("Flight disabled")
            end
        end
    end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- FLIGHT LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    -- Only run if character is loaded
    if not char or not root or not humanoid or not gyro or not vel then return end

    if flying then
        local cf = workspace.CurrentCamera.CFrame
        local moveDir = Vector3.new(
            (d and 1 or 0) + (a and -1 or 0),
            (q and 1 or 0) + (e and -1 or 0),
            (s and 1 or 0) + (w and -1 or 0)
        )
        moveDir = cf:VectorToWorldSpace(moveDir)
        vel.Velocity = moveDir * 80   -- client side visual speed (server caps)
        gyro.CFrame = cf
    else
        -- When not flying, let normal physics take over
        vel.Velocity = Vector3.new(0, 0, 0)
    end
end)

------------------------------------------------------------------
-- HEAT VISION SYSTEM
------------------------------------------------------------------
local heatVisionActive = false
local heatVisionBeams = {}
local lastHeatVisionTime = 0

-- Create continuous heat vision beams from eyes
local function createHeatVisionBeams(startPos, endPos)
    -- Remove old beams
    for _, beam in pairs(heatVisionBeams) do
        if beam and beam.Parent then
            beam:Destroy()
        end
    end
    heatVisionBeams = {}

    -- Create two beams (one for each eye)
    local eyeOffset = 0.3
    local headCFrame = char.Head.CFrame
    local rightEye = headCFrame * CFrame.new(eyeOffset, 0, 0)
    local leftEye = headCFrame * CFrame.new(-eyeOffset, 0, 0)

    for i, eyePos in pairs({rightEye.Position, leftEye.Position}) do
        local beam = Instance.new("Part")
        beam.Name = "HeatVisionBeam"
        beam.Anchored = true
        beam.CanCollide = false
        beam.Size = Vector3.new(0.1, 0.1, (endPos - eyePos).Magnitude)
        beam.CFrame = CFrame.new(eyePos, endPos) * CFrame.new(0, 0, -beam.Size.Z / 2)
        beam.BrickColor = BrickColor.new("Bright red")
        beam.Material = Enum.Material.Neon
        beam.Transparency = 0.2
        beam.Parent = workspace

        -- Add glow effect
        local pointLight = Instance.new("PointLight")
        pointLight.Color = Color3.fromRGB(255, 0, 0)
        pointLight.Brightness = 2
        pointLight.Range = 10
        pointLight.Parent = beam

        table.insert(heatVisionBeams, beam)
    end
end

-- Update heat vision continuously
local function updateHeatVision()
    if not heatVisionActive or not char or not char:FindFirstChild("Head") then
        return
    end

    local camera = workspace.CurrentCamera
    local head = char.Head

    -- Raycast from camera forward
    local rayOrigin = camera.CFrame.Position
    local rayDirection = camera.CFrame.LookVector * 500

    local raycastParams = RaycastParams.new()
    raycastParams.FilterDescendantsInstances = {char}
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist

    local raycastResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)
    local hitPos = raycastResult and raycastResult.Position or (rayOrigin + rayDirection)

    -- Create/update beams
    createHeatVisionBeams(head.Position, hitPos)

    -- Fire server event for damage (limit rate)
    local currentTime = tick()
    if currentTime - lastHeatVisionTime > 0.1 then -- 10 times per second max
        powerRemote:FireServer("HeatVision", hitPos)
        lastHeatVisionTime = currentTime
    end
end

-- Heat vision loop
RunService.Heartbeat:Connect(function()
    if heatVisionActive then
        updateHeatVision()
    end
end)

------------------------------------------------------------------
-- HEAT VISION INPUT
------------------------------------------------------------------
-- Mouse/touch input for heat vision
UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end

    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = true
        print("Heat vision activated!")
    end
end)

UserInputService.InputEnded:Connect(function(input, gpe)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        heatVisionActive = false

        -- Clean up beams
        for _, beam in pairs(heatVisionBeams) do
            if beam and beam.Parent then
                beam:Destroy()
            end
        end
        heatVisionBeams = {}
        print("Heat vision deactivated!")
    end
end)
