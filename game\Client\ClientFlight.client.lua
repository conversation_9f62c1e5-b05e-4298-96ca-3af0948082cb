-- ClientFlight
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = game.Players.LocalPlayer
local char = player.Character or player.CharacterAdded:Wait()
local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
local remote = ReplicatedStorage:WaitForChild("SupermanShared"):WaitForChild("FlightRemote")

------------------------------------------------------------------
-- BODY MOVERS
------------------------------------------------------------------
local gyro = Instance.new("BodyGyro")
gyro.MaxTorque = Vector3.new(400000, 400000, 400000)
gyro.P = 10000
gyro.Parent = root

local vel = Instance.new("BodyVelocity")
vel.MaxForce = Vector3.new(400000, 400000, 400000)
vel.Parent = root

------------------------------------------------------------------
-- INPUT
------------------------------------------------------------------
local flying = false
local w, a, s, d, q, e = false, false, false, false, false, false

local function updateInputs(input, state)
    local key = input.KeyCode
    if key == Enum.KeyCode.W then w = state end
    if key == Enum.KeyCode.A then a = state end
    if key == Enum.KeyCode.S then s = state end
    if key == Enum.KeyCode.D then d = state end
    if key == Enum.KeyCode.Space then q = state end
    if key == Enum.KeyCode.LeftShift then e = state end
end

UserInputService.InputBegan:Connect(function(input) updateInputs(input, true) end)
UserInputService.InputEnded:Connect(function(input) updateInputs(input, false) end)

------------------------------------------------------------------
-- LOOP
------------------------------------------------------------------
RunService.RenderStepped:Connect(function(dt)
    if not flying then
        if humanoid:GetState() == Enum.HumanoidStateType.Freefall or root.Velocity.Y < -50 then
            flying = true
            humanoid.PlatformStand = true
        end
    else
        local cf = workspace.CurrentCamera.CFrame
        local moveDir = Vector3.new(
            (d and 1 or 0) + (a and -1 or 0),
            (q and 1 or 0) + (e and -1 or 0),
            (s and 1 or 0) + (w and -1 or 0)
        )
        moveDir = cf:VectorToWorldSpace(moveDir)
        vel.Velocity = moveDir * 80   -- client side visual speed (server caps)
        gyro.CFrame = cf
    end
end)

------------------------------------------------------------------
-- POWER VISUALS
------------------------------------------------------------------
-- render beams / fx
ReplicatedStorage:WaitForChild("SupermanShared").PowerRemote.OnClientEvent:Connect(function(powerName, startPos, endPos)
    if powerName == "HeatVision" then
        local beam = Instance.new("Part")
        beam.Anchored = true; beam.CanCollide = false; beam.Size = Vector3.new(0.2,0.2,(endPos-startPos).Magnitude)
        beam.CFrame = CFrame.new(startPos, endPos) * CFrame.new(0,0,-beam.Size.Z/2)
        beam.BrickColor = BrickColor.new("Bright red")
        beam.Material = Enum.Material.Neon
        beam.Parent = workspace
        game:GetService("Debris"):AddItem(beam, 0.15)
    end
end)

------------------------------------------------------------------
-- POWER INPUT
------------------------------------------------------------------
-- Click / tap to fire
local function fire()
    local cam = workspace.CurrentCamera
    local params = RaycastParams.new()
    params.FilterDescendantsInstances = {char}
    params.FilterType = Enum.RaycastFilterType.Blacklist

    local origin, dir
    if UserInputService.TouchEnabled and not UserInputService.MouseEnabled then
        origin = cam.CFrame.Position
        dir = (cam.CFrame * CFrame.new(0, 0, -1)).Position - origin
    else
        local mouse = player:GetMouse()
        origin = cam.CFrame.Position
        dir = (mouse.Hit.Position - origin).Unit
    end

    local res = workspace:Raycast(origin, dir * 500, params)
    local hit = res and res.Position or origin + dir * 500

    ReplicatedStorage:WaitForChild("SupermanShared").PowerRemote:FireServer("HeatVision", hit)
end

UserInputService.InputBegan:Connect(function(input, gpe)
    if gpe then return end
    if input.UserInputType == Enum.UserInputType.MouseButton1 or
       input.UserInputType == Enum.UserInputType.Touch then
        fire()
    end
end)
