-- HeatVision module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

-- Track last damage time per player to prevent spam
local lastDamageTime = {}

return function(player, mouseHit)
    local char = player.Character
    if not char then return end

    local head = char:Find<PERSON>irs<PERSON><PERSON>hild("Head")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid")
    if not head or humanoid.Health <= 0 then return end

    -- Rate limiting - only allow damage every 0.25 seconds per player
    local currentTime = tick()
    if lastDamageTime[player] and currentTime - lastDamageTime[player] < Config.POWERS.HeatVision.cd then
        return
    end
    lastDamageTime[player] = currentTime

    -- Raycast from head position toward the hit point
    local rayOrigin = head.Position
    local rayDirection = (mouseHit - rayOrigin).Unit * 500

    local raycastParams = RaycastParams.new()
    raycastParams.FilterDescendantsInstances = {char}
    raycastParams.FilterType = Enum.RaycastFilterType.Blacklist

    local raycastResult = workspace:Raycast(rayOrigin, rayDirection, raycastParams)

    if raycastResult then
        local hitPart = raycastResult.Instance
        local hitPosition = raycastResult.Position

        -- Check if we hit a character
        if hitPart and hitPart.Parent:FindFirstChild("Humanoid") then
            local enemyHumanoid = hitPart.Parent.Humanoid
            local enemyPlayer = game.Players:GetPlayerFromCharacter(hitPart.Parent)

            -- Don't damage the caster
            if enemyPlayer ~= player then
                enemyHumanoid:TakeDamage(Config.POWERS.HeatVision.dmg)
                print("🔥", player.Name, "dealt", Config.POWERS.HeatVision.dmg, "heat vision damage to", hitPart.Parent.Name)
            end
        end
    end
end
