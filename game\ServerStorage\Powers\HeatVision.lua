-- HeatVision module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

-- Track last damage time per player to prevent spam
local lastDamageTime = {}

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Rate limiting - only allow damage every 0.25 seconds per player
    local currentTime = tick()
    if lastDamageTime[player] and currentTime - lastDamageTime[player] < Config.POWERS.HeatVision.cd then
        return
    end
    lastDamageTime[player] = currentTime

    local ray = Ray.new(root.Position, (mouseHit - root.Position).Unit * 300)
    local part, pos = workspace:FindPartOnRay(ray, char)

    -- damage & fx
    if part and part.Parent:FindFirstChild("Humanoid") then
        local enemyHum = part.Parent.Humanoid
        enemyHum:TakeDamage(Config.POWERS.HeatVision.dmg)
        print("🔥", player.Name, "dealt", Config.POWERS.HeatVision.dmg, "heat vision damage to", part.Parent.Name)
    end
end
