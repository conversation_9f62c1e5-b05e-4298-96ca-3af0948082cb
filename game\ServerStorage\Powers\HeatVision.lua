local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    local ray = Ray.new(root.Position, (mouseHit - root.Position).Unit * 300)
    local part, pos = workspace:FindPartOnRay(ray, char)

    if part and part.Parent:FindFirstChild("Humanoid") then
        part.Parent.Humanoid:TakeDamage(Config.POWERS.HeatVision.dmg)
    end

    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("HeatVision", root.Position, pos)
end
