-- HeatVision module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    print("🔥 Heat Vision activated by", player.Name, "at", mouseHit)

    local char = player.Character
    if not char then
        print("❌ No character found for", player.Name)
        return
    end

    local root = char:Find<PERSON>irstChild("HumanoidRootPart")
    local humanoid = char:Find<PERSON><PERSON><PERSON><PERSON>hild("Humanoid")
    if not root or humanoid.Health <= 0 then
        print("❌ No root part or dead character for", player.Name)
        return
    end

    local ray = Ray.new(root.Position, (mouseHit - root.Position).Unit * 300)
    local part, pos = workspace:FindPartOnRay(ray, char)

    print("🎯 Ray hit:", part and part.Name or "nothing", "at", pos)

    -- damage & fx
    if part and part.Parent:Find<PERSON>irst<PERSON>hild("Humanoid") then
        local enemyHum = part.Parent.Humanoid
        enemyHum:TakeDamage(Config.POWERS.HeatVision.dmg)
        print("💥 Dealt", Config.POWERS.HeatVision.dmg, "damage to", part.Parent.Name)
    end

    -- visual (fire small beam on all clients)
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("HeatVision", root.Position, pos)
    print("✨ Heat vision beam sent to all clients")
end
