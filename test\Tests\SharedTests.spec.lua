-- Unit tests for Superman Config module

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Config = require(ReplicatedStorage.SupermanShared.Config)

return function()
    describe("Superman Config", function()
        describe("Speed Settings", function()
            it("should have max speed defined", function()
                expect(Config.MAX_SPEED).to.equal(120)
            end)

            it("should have speed multiplier", function()
                expect(Config.SPEED_MULTIPLIER).to.equal(1.5)
            end)

            it("should have dev product ID", function()
                expect(Config.DEV_PRODUCT_SPEED).to.be.ok()
                expect(type(Config.DEV_PRODUCT_SPEED)).to.equal("number")
            end)
        end)

        describe("Powers Configuration", function()
            it("should have heat vision power", function()
                expect(Config.POWERS.HeatVision).to.be.ok()
                expect(Config.POWERS.HeatVision.dmg).to.equal(25)
                expect(Config.POWERS.HeatVision.cd).to.equal(0.25)
            end)

            it("should have freeze breath power", function()
                expect(Config.POWERS.FreezeBreath).to.be.ok()
                expect(Config.POWERS.FreezeBreath.duration).to.equal(3)
                expect(Config.POWERS.FreezeBreath.cd).to.equal(3)
            end)

            it("should have super strength power", function()
                expect(Config.POWERS.SuperStrength).to.be.ok()
                expect(Config.POWERS.SuperStrength.dmg).to.equal(50)
                expect(Config.POWERS.SuperStrength.cd).to.equal(1)
            end)
        end)
    end)
end
