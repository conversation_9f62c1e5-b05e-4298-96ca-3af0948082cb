-- Unit tests for Shared module

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Shared = require(ReplicatedStorage.Shared)

return function()
    describe("Shared Module", function()
        describe("Constants", function()
            it("should have correct game name", function()
                expect(Shared.Constants.GAME_NAME).to.equal("SupaMan")
            end)
            
            it("should have version", function()
                expect(Shared.Constants.VERSION).to.be.ok()
            end)
        end)
        
        describe("Utility Functions", function()
            it("should deep copy tables", function()
                local original = {a = 1, b = {c = 2}}
                local copy = Shared.deepCopy(original)
                
                expect(copy.a).to.equal(1)
                expect(copy.b.c).to.equal(2)
                expect(copy).to.never.equal(original)
                expect(copy.b).to.never.equal(original.b)
            end)
            
            it("should format time correctly", function()
                expect(Shared.formatTime(65)).to.equal("01:05")
                expect(Shared.formatTime(0)).to.equal("00:00")
                expect(Shared.formatTime(3661)).to.equal("61:01")
            end)
        end)
    end)
end
